<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products_substrates_convections', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('product_id');
            // $table->foreign('product_id')
            //     ->references('id')
            //     ->on('products')
            //         ->onDelete('cascade')
            //         ->onUpdate('no action');

            $table->unsignedBigInteger('product_substrate_id');
            // $table->foreign('product_substrate_id')
            //     ->references('id')
            //     ->on('products')
            //         ->onDelete('cascade')
            //         ->onUpdate('no action');

            $table->unsignedBigInteger('qos_convection_id');
            // $table->foreign('qos_convection_id')
            //     ->references('id')
            //     ->on('products')
            //         ->onDelete('cascade')
            //         ->onUpdate('no action');

            $table->timestampsDefault();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products_substrates_convections');
    }
};
