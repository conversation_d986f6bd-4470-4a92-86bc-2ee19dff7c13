<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products_pricing_steps', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('pricing_id');
            $table->foreign('pricing_id')
                ->references('id')
                ->on('products_pricing')
                    ->onDelete('cascade')
                    ->onUpdate('no action');

            $table->integer('amount_start')->unsigned();
            $table->integer('amount_end')->unsigned();
            
            $table->decimal('price_excl', 10 , 5 )->nullable();
            $table->decimal('price_back_excl', 10 , 5 )->nullable();

            $table->timestampsDefault();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products_pricing_steps');
    }
};
