<?php

use App\Models\Property;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('properties_values_relations', function (Blueprint $table) {

            $table->id();

            $table->foreignId('property_id')
                ->references('id')
                ->on('properties')
                    ->onDelete('cascade')
                    ->onUpdate('no action');

            $table->foreignId('property_value_id')
                ->references('id')
                ->on('properties_values')
                    ->onDelete('cascade')
                    ->onUpdate('no action');

            $table->morphs('relatable');

            $table->unique(['property_id', 'property_value_id', 'relatable_id' , 'relatable_type'], 'relatable_property_value');

            $table->timestampsDefault(); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('properties_values_relations');
    }
};
