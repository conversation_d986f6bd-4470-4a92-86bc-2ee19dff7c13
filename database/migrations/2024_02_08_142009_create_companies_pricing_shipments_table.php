<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies_pricing_shipments', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('company_id');

            $table->foreign('company_id')
                ->references('id')
                ->on('companies')
                    ->onDelete('cascade')
                    ->onUpdate('no action');

            $table->string('methods', 600)->nullable();
            $table->string('days', 600)->nullable();
            $table->enum('conditioning', ['always','minimal','maximal','range'])->nullable();
            $table->decimal('amount_start', 10 , 2 )->nullable()->unsigned();
            $table->decimal('amount_stop', 10 , 2 )->nullable()->unsigned();
            $table->enum('discount', ['percentage', 'fixed'])->nullable();
            $table->enum('direction', ['+', '-'])->nullable();
            $table->decimal('value', 10 , 2 )->nullable();
                
            $table->timestampsDefault();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies_pricing_shipments');
    }
};
