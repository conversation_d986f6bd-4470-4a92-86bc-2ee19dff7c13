<?php

use App\Enums\DocumentClassification;
use App\Enums\ItemType;
use App\Enums\ItemUnitTypeSales;
use App\Enums\ItemUnitTypeWarehouse;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function shouldRun(): bool
    {
        return false;
    }

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {

            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            $table->id();
            $table->morphs('documentable');
            $table->enum('classification', DocumentClassification::values())->nullable();

            $table->timestampsDefault();
            $table->softDeletes();

            $table->unique(['documentable_type', 'documentable_id', 'classification'], 'documents_unique');
        });

        Schema::create('documents_versions', function (Blueprint $table) {

            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            $table->id();
            $table->documentId('cascade');
            $table->string('name', 300);
            $table->string('name_origin', 600)->nullable()->default(null);
            $table->string('extension', 3)->nullable()->default(null);
            $table->string('mimetype', 200)->nullable()->default(null);
            $table->unsignedInteger('version')->default(1);
            $table->unsignedBigInteger('size');

            $table->has('image', false);
            $table->has('html', false);
            $table->has('data', false);
            $table->is('processed', true);

            $table->timestampsDefault();
            $table->softDeletes();

            $table->unique(['document_id', 'version'], 'documents_versions_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents_versions');
        Schema::dropIfExists('documents');
    }
};
