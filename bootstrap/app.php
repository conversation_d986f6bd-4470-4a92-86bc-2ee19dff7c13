<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        using: function () {

            Route::middleware('pdf')
                ->group(base_path('routes/pdf.php'));

            Route::middleware('admin')
                ->group(base_path('routes/admin.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            Route::middleware('actions')
                ->group(base_path('routes/actions.php'));

            
        }        
    )
    ->withMiddleware(function (Middleware $middleware) {

        $middleware->alias([
            'auth' => \App\Http\Middleware\Authenticate::class,
        ]);

        $middleware->use([
            \Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks::class,
           // \Illuminate\Http\Middleware\TrustHosts::class,
            \Illuminate\Http\Middleware\TrustProxies::class,
           // \Illuminate\Http\Middleware\HandleCors::class,
            \Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance::class,
            \Illuminate\Http\Middleware\ValidatePostSize::class,
            \Illuminate\Foundation\Http\Middleware\TrimStrings::class,
            \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
            \App\Http\Middleware\IpRestrictionMiddleware::class,
            
        ]);

        $middleware->prependToGroup('admin', [
            \App\Http\Middleware\AppBootstrap::class,  
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \App\Http\Middleware\AppLocale::class,  
            \App\Http\Middleware\HandleCheckboxes::class,    
            \DipeshSukhia\LaravelHtmlMinify\Middleware\LaravelMinifyHtml::class,
        ]);

        $middleware->prependToGroup('actions', [
            \App\Http\Middleware\AppBootstrap::class,  
          //  \Illuminate\Routing\Middleware\ThrottleRequests::class.':actions',
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \App\Http\Middleware\AppLocale::class,  
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \DipeshSukhia\LaravelHtmlMinify\Middleware\LaravelMinifyHtml::class,
        ]);

        $middleware->prependToGroup('pdf', [
            \App\Http\Middleware\AppBootstrap::class,  
            \Illuminate\Session\Middleware\StartSession::class,
            \App\Http\Middleware\AppLocale::class,  
            \App\Http\Middleware\AuthHeader::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \DipeshSukhia\LaravelHtmlMinify\Middleware\LaravelMinifyHtml::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();